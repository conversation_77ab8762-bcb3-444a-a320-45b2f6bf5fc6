import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/theme/app_theme.dart';
import '../core/constants/app_constants.dart';

class ThemeNotifier extends StateNotifier<AppThemeMode> {
  ThemeNotifier() : super(AppThemeMode.light) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final themeString = prefs.getString(AppConstants.themeKey) ?? 'light';
    
    switch (themeString) {
      case 'dark':
        state = AppThemeMode.dark;
        break;
      case 'sepia':
        state = AppThemeMode.sepia;
        break;
      default:
        state = AppThemeMode.light;
    }
  }

  Future<void> setTheme(AppThemeMode theme) async {
    state = theme;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.themeKey, theme.name);
  }

  Future<void> toggleTheme() async {
    switch (state) {
      case AppThemeMode.light:
        await setTheme(AppThemeMode.dark);
        break;
      case AppThemeMode.dark:
        await setTheme(AppThemeMode.sepia);
        break;
      case AppThemeMode.sepia:
        await setTheme(AppThemeMode.light);
        break;
    }
  }
}

// Reading Settings Notifier
class ReadingSettingsNotifier extends StateNotifier<ReadingSettings> {
  ReadingSettingsNotifier() : super(const ReadingSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    state = ReadingSettings(
      fontSize: prefs.getDouble(AppConstants.fontSizeKey) ?? AppConstants.defaultFontSize,
      brightness: prefs.getDouble(AppConstants.brightnessKey) ?? AppConstants.defaultBrightness,
      readingMode: prefs.getString(AppConstants.readingModeKey) ?? 'paginated',
    );
  }

  Future<void> setFontSize(double fontSize) async {
    state = state.copyWith(fontSize: fontSize);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(AppConstants.fontSizeKey, fontSize);
  }

  Future<void> setBrightness(double brightness) async {
    state = state.copyWith(brightness: brightness);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(AppConstants.brightnessKey, brightness);
  }

  Future<void> setReadingMode(String readingMode) async {
    state = state.copyWith(readingMode: readingMode);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.readingModeKey, readingMode);
  }

  Future<void> increaseFontSize() async {
    final newSize = (state.fontSize + 1).clamp(
      AppConstants.minFontSize,
      AppConstants.maxFontSize,
    );
    await setFontSize(newSize);
  }

  Future<void> decreaseFontSize() async {
    final newSize = (state.fontSize - 1).clamp(
      AppConstants.minFontSize,
      AppConstants.maxFontSize,
    );
    await setFontSize(newSize);
  }
}

class ReadingSettings {
  final double fontSize;
  final double brightness;
  final String readingMode;

  const ReadingSettings({
    this.fontSize = AppConstants.defaultFontSize,
    this.brightness = AppConstants.defaultBrightness,
    this.readingMode = 'paginated',
  });

  ReadingSettings copyWith({
    double? fontSize,
    double? brightness,
    String? readingMode,
  }) {
    return ReadingSettings(
      fontSize: fontSize ?? this.fontSize,
      brightness: brightness ?? this.brightness,
      readingMode: readingMode ?? this.readingMode,
    );
  }
}

// Providers
final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeMode>((ref) {
  return ThemeNotifier();
});

final readingSettingsProvider = StateNotifierProvider<ReadingSettingsNotifier, ReadingSettings>((ref) {
  return ReadingSettingsNotifier();
});

// Theme Data Provider
final themeDataProvider = Provider<ThemeData>((ref) {
  final themeMode = ref.watch(themeProvider);
  
  switch (themeMode) {
    case AppThemeMode.light:
      return AppTheme.lightTheme;
    case AppThemeMode.dark:
      return AppTheme.darkTheme;
    case AppThemeMode.sepia:
      return AppTheme.sepiaTheme;
  }
});

// Brightness Provider for PDF Reader
final brightnessProvider = Provider<double>((ref) {
  return ref.watch(readingSettingsProvider).brightness;
});

// Font Size Provider for PDF Reader
final fontSizeProvider = Provider<double>((ref) {
  return ref.watch(readingSettingsProvider).fontSize;
});
