// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bookapp/core/theme/app_theme.dart';
import 'package:bookapp/shared/widgets/custom_button.dart';

void main() {
  testWidgets('Custom Button Widget Test', (WidgetTester tester) async {
    // Build our custom button widget
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body: CustomButton(
            text: 'Test Button',
            onPressed: () {},
          ),
        ),
      ),
    );

    // Verify that the button is rendered
    expect(find.text('Test Button'), findsOneWidget);
    expect(find.byType(CustomButton), findsOneWidget);
  });

  testWidgets('Theme Provider Test', (WidgetTester tester) async {
    // Test that themes are properly configured
    expect(AppTheme.lightTheme, isNotNull);
    expect(AppTheme.darkTheme, isNotNull);
    expect(AppTheme.sepiaTheme, isNotNull);
  });
}
