import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/constants/app_constants.dart';
import '../models/user.dart';
import '../models/book.dart';
import '../models/category.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();
  
  SupabaseService._();

  SupabaseClient get client => Supabase.instance.client;
  User? get currentUser => client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
  }

  // Authentication Methods
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? fullName,
    String? institution,
    String? studentId,
  }) async {
    try {
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'institution': institution,
          'student_id': studentId,
        },
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // User Profile Methods
  Future<AppUser?> getUserProfile(String userId) async {
    try {
      final response = await client
          .from('profiles')
          .select()
          .eq('id', userId)
          .single();
      
      return AppUser.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<void> updateUserProfile(AppUser user) async {
    try {
      await client
          .from('profiles')
          .update(user.toJson())
          .eq('id', user.id);
    } catch (e) {
      rethrow;
    }
  }

  // Books Methods
  Future<List<Book>> getBooks({
    String? categoryId,
    String? searchQuery,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = client
          .from(AppConstants.booksTable)
          .select('*, categories(*)');

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('title.ilike.%$searchQuery%,author.ilike.%$searchQuery%');
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => Book.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<Book?> getBook(String bookId) async {
    try {
      final response = await client
          .from(AppConstants.booksTable)
          .select('*, categories(*)')
          .eq('id', bookId)
          .single();

      return Book.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Categories Methods
  Future<List<Category>> getCategories() async {
    try {
      final response = await client
          .from(AppConstants.categoriesTable)
          .select()
          .order('sort_order', ascending: true);

      return (response as List)
          .map((json) => Category.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  // Storage Methods
  Future<String> uploadFile({
    required String bucket,
    required String path,
    required Uint8List fileBytes,
    String? contentType,
  }) async {
    try {
      await client.storage
          .from(bucket)
          .uploadBinary(path, fileBytes, fileOptions: FileOptions(
            contentType: contentType,
          ));

      return client.storage.from(bucket).getPublicUrl(path);
    } catch (e) {
      rethrow;
    }
  }

  Future<List<int>> downloadFile({
    required String bucket,
    required String path,
  }) async {
    try {
      final response = await client.storage
          .from(bucket)
          .download(path);
      
      return response;
    } catch (e) {
      rethrow;
    }
  }

  String getPublicUrl({
    required String bucket,
    required String path,
  }) {
    return client.storage.from(bucket).getPublicUrl(path);
  }

  // Real-time subscriptions
  RealtimeChannel subscribeToBooks(void Function(List<Book>) onData) {
    return client
        .channel('books')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: AppConstants.booksTable,
          callback: (payload) async {
            // Fetch updated books and call onData
            final books = await getBooks();
            onData(books);
          },
        )
        .subscribe();
  }

  void unsubscribe(RealtimeChannel channel) {
    client.removeChannel(channel);
  }
}
