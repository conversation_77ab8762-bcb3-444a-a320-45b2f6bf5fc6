import 'package:equatable/equatable.dart';

class AppUser extends Equatable {
  final String id;
  final String email;
  final String? fullName;
  final String? avatarUrl;
  final String? institution;
  final String? studentId;
  final UserRole role;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  final UserPreferences preferences;

  const AppUser({
    required this.id,
    required this.email,
    this.fullName,
    this.avatarUrl,
    this.institution,
    this.studentId,
    this.role = UserRole.student,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.isActive = true,
    this.preferences = const UserPreferences(),
  });

  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      institution: json['institution'] as String?,
      studentId: json['student_id'] as String?,
      role: UserRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => UserRole.student,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
      isActive: json['is_active'] as bool? ?? true,
      preferences: json['preferences'] != null
          ? UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>)
          : const UserPreferences(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'institution': institution,
      'student_id': studentId,
      'role': role.name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_active': isActive,
      'preferences': preferences.toJson(),
    };
  }

  AppUser copyWith({
    String? id,
    String? email,
    String? fullName,
    String? avatarUrl,
    String? institution,
    String? studentId,
    UserRole? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    bool? isActive,
    UserPreferences? preferences,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      institution: institution ?? this.institution,
      studentId: studentId ?? this.studentId,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        fullName,
        avatarUrl,
        institution,
        studentId,
        role,
        createdAt,
        updatedAt,
        lastLoginAt,
        isActive,
        preferences,
      ];
}

enum UserRole {
  student,
  teacher,
  admin,
}

class UserPreferences extends Equatable {
  final String themeMode;
  final double fontSize;
  final double brightness;
  final String readingMode;
  final bool autoSync;
  final bool notifications;
  final String language;

  const UserPreferences({
    this.themeMode = 'light',
    this.fontSize = 16.0,
    this.brightness = 1.0,
    this.readingMode = 'paginated',
    this.autoSync = true,
    this.notifications = true,
    this.language = 'en',
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      themeMode: json['theme_mode'] as String? ?? 'light',
      fontSize: (json['font_size'] as num?)?.toDouble() ?? 16.0,
      brightness: (json['brightness'] as num?)?.toDouble() ?? 1.0,
      readingMode: json['reading_mode'] as String? ?? 'paginated',
      autoSync: json['auto_sync'] as bool? ?? true,
      notifications: json['notifications'] as bool? ?? true,
      language: json['language'] as String? ?? 'en',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'theme_mode': themeMode,
      'font_size': fontSize,
      'brightness': brightness,
      'reading_mode': readingMode,
      'auto_sync': autoSync,
      'notifications': notifications,
      'language': language,
    };
  }

  UserPreferences copyWith({
    String? themeMode,
    double? fontSize,
    double? brightness,
    String? readingMode,
    bool? autoSync,
    bool? notifications,
    String? language,
  }) {
    return UserPreferences(
      themeMode: themeMode ?? this.themeMode,
      fontSize: fontSize ?? this.fontSize,
      brightness: brightness ?? this.brightness,
      readingMode: readingMode ?? this.readingMode,
      autoSync: autoSync ?? this.autoSync,
      notifications: notifications ?? this.notifications,
      language: language ?? this.language,
    );
  }

  @override
  List<Object?> get props => [
        themeMode,
        fontSize,
        brightness,
        readingMode,
        autoSync,
        notifications,
        language,
      ];
}
