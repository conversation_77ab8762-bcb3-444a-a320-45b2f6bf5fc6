# bookapp

A new Flutter project.

## Getting Started

# BookApp - Institutional Digital Reading Platform

An institutional book reading app with Kindle-like features built with Flutter and Supabase, designed for academic institutions.

## 🚀 Features

### Phase 1: Foundations ✅
- ✅ Flutter project setup with Material 3 design
- ✅ Supabase integration for backend services
- ✅ Authentication system (Sign up/Sign in)
- ✅ State management with Riverpod
- ✅ Custom theming (Light, Dark, Sepia modes)
- ✅ Responsive UI components

### Phase 2: Reading Experience (Coming Soon)
- [ ] PDF viewer integration with Syncfusion
- [ ] Reading settings (brightness, font size, reading mode)
- [ ] Bookmarks, highlights, and notes
- [ ] Progress tracking

### Phase 3: Offline + Library (Coming Soon)
- [ ] Download functionality for offline reading
- [ ] Local storage with Hive
- [ ] Sync bookmarks and notes

### Phase 4: Personalization (Coming Soon)
- [ ] Onboarding customization
- [ ] Personalized home feed
- [ ] Advanced search and filtering

### Phase 5: Polish & Launch (Coming Soon)
- [ ] Accessibility features
- [ ] Animations and micro-interactions
- [ ] App store deployment

## 🛠 Tech Stack

| Component | Technology |
|-----------|------------|
| Frontend | Flutter (Material 3) |
| State Management | Riverpod |
| Backend | Supabase |
| Database | PostgreSQL (Supabase) |
| Storage | Supabase Storage |
| Authentication | Supabase Auth |
| PDF Viewer | Syncfusion Flutter PDF Viewer |
| Local Storage | Hive |
| Navigation | Go Router |

## 📱 Screenshots

*Screenshots will be added as features are implemented*

## 🔧 Setup Instructions

### Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / VS Code
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bookapp
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Supabase**
   - Create a new project on [Supabase](https://supabase.com)
   - Copy your project URL and anon key
   - Update `lib/core/constants/app_constants.dart`:
   ```dart
   static const String supabaseUrl = 'YOUR_SUPABASE_URL';
   static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

4. **Set up Supabase Database**

   Create the following tables in your Supabase database:

   ```sql
   -- Users profiles table
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users ON DELETE CASCADE,
     email TEXT,
     full_name TEXT,
     avatar_url TEXT,
     institution TEXT,
     student_id TEXT,
     role TEXT DEFAULT 'student',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     last_login_at TIMESTAMP WITH TIME ZONE,
     is_active BOOLEAN DEFAULT TRUE,
     preferences JSONB DEFAULT '{}',
     PRIMARY KEY (id)
   );

   -- Categories table
   CREATE TABLE categories (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     name TEXT NOT NULL,
     description TEXT,
     icon_url TEXT,
     color TEXT DEFAULT '#1976D2',
     sort_order INTEGER DEFAULT 0,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Books table
   CREATE TABLE books (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     title TEXT NOT NULL,
     author TEXT NOT NULL,
     description TEXT,
     cover_url TEXT,
     pdf_url TEXT NOT NULL,
     category_id UUID REFERENCES categories(id),
     total_pages INTEGER DEFAULT 0,
     isbn TEXT,
     publisher TEXT,
     published_date DATE,
     tags TEXT[] DEFAULT '{}',
     rating DECIMAL(2,1) DEFAULT 0.0,
     rating_count INTEGER DEFAULT 0,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

5. **Run the app**
   ```bash
   flutter run
   ```

## 🧪 Testing

Run tests with:
```bash
flutter test
```

Run code analysis:
```bash
flutter analyze
```

## 📁 Project Structure

```
lib/
├── core/
│   ├── constants/          # App constants
│   └── theme/             # App themes
├── features/
│   ├── auth/              # Authentication screens
│   ├── home/              # Home screen
│   ├── splash/            # Splash screen
│   ├── books/             # Book-related features (coming soon)
│   └── library/           # User library (coming soon)
├── models/                # Data models
├── providers/             # Riverpod providers
├── services/              # External services (Supabase)
├── shared/
│   └── widgets/           # Reusable widgets
└── main.dart              # App entry point
```

## 🎨 Design System

The app follows Material 3 design principles with custom theming:

- **Light Theme**: Clean, modern interface for daytime reading
- **Dark Theme**: Easy on the eyes for low-light environments
- **Sepia Theme**: Warm, paper-like feel for comfortable reading

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Flutter](https://flutter.dev/) for the amazing framework
- [Supabase](https://supabase.com/) for the backend infrastructure
- [Syncfusion](https://www.syncfusion.com/flutter-widgets) for PDF viewing capabilities
- [Riverpod](https://riverpod.dev/) for state management

## 📞 Support

For support, email [<EMAIL>] or create an issue in this repository.
