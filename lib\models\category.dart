import 'package:equatable/equatable.dart';

class Category extends Equatable {
  final String id;
  final String name;
  final String description;
  final String? iconUrl;
  final String color;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int bookCount;
  final int sortOrder;

  const Category({
    required this.id,
    required this.name,
    required this.description,
    this.iconUrl,
    required this.color,
    required this.createdAt,
    required this.updatedAt,
    this.bookCount = 0,
    this.sortOrder = 0,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['icon_url'] as String?,
      color: json['color'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      bookCount: json['book_count'] as int? ?? 0,
      sortOrder: json['sort_order'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_url': iconUrl,
      'color': color,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'book_count': bookCount,
      'sort_order': sortOrder,
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    String? color,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? bookCount,
    int? sortOrder,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      bookCount: bookCount ?? this.bookCount,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        iconUrl,
        color,
        createdAt,
        updatedAt,
        bookCount,
        sortOrder,
      ];
}
