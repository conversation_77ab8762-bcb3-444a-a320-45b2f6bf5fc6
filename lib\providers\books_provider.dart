import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/book.dart';
import '../models/category.dart';
import '../services/supabase_service.dart';
import 'auth_provider.dart';

// Books State
class BooksState {
  final List<Book> books;
  final List<Category> categories;
  final bool isLoading;
  final String? error;

  const BooksState({
    this.books = const [],
    this.categories = const [],
    this.isLoading = false,
    this.error,
  });

  BooksState copyWith({
    List<Book>? books,
    List<Category>? categories,
    bool? isLoading,
    String? error,
  }) {
    return BooksState(
      books: books ?? this.books,
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Books Notifier
class BooksNotifier extends StateNotifier<BooksState> {
  final SupabaseService _supabaseService;

  BooksNotifier(this._supabaseService) : super(const BooksState()) {
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Load categories and books in parallel
      final results = await Future.wait([
        _supabaseService.getCategories(),
        _supabaseService.getBooks(),
      ]);

      final categories = results[0] as List<Category>;
      final books = results[1] as List<Book>;

      state = state.copyWith(
        categories: categories,
        books: books,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadBooks({String? categoryId, String? searchQuery}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final books = await _supabaseService.getBooks(
        categoryId: categoryId,
        searchQuery: searchQuery,
      );

      state = state.copyWith(
        books: books,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> searchBooks(String query) async {
    if (query.isEmpty) {
      await loadBooks();
      return;
    }
    
    await loadBooks(searchQuery: query);
  }

  Future<void> loadBooksByCategory(String categoryId) async {
    await loadBooks(categoryId: categoryId);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final booksProvider = StateNotifierProvider<BooksNotifier, BooksState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return BooksNotifier(supabaseService);
});

// Convenience providers
final categoriesProvider = Provider<List<Category>>((ref) {
  return ref.watch(booksProvider).categories;
});

final booksListProvider = Provider<List<Book>>((ref) {
  return ref.watch(booksProvider).books;
});

final booksLoadingProvider = Provider<bool>((ref) {
  return ref.watch(booksProvider).isLoading;
});

final booksErrorProvider = Provider<String?>((ref) {
  return ref.watch(booksProvider).error;
});

// Get books by category
final booksByCategoryProvider = Provider.family<List<Book>, String>((ref, categoryId) {
  final books = ref.watch(booksListProvider);
  return books.where((book) => book.categoryId == categoryId).toList();
});

// Get category by ID
final categoryByIdProvider = Provider.family<Category?, String>((ref, categoryId) {
  final categories = ref.watch(categoriesProvider);
  try {
    return categories.firstWhere((category) => category.id == categoryId);
  } catch (e) {
    return null;
  }
});
