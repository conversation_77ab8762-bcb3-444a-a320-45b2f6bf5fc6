import 'package:equatable/equatable.dart';

class Book extends Equatable {
  final String id;
  final String title;
  final String author;
  final String description;
  final String? coverUrl;
  final String pdfUrl;
  final String categoryId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int totalPages;
  final String? isbn;
  final String? publisher;
  final DateTime? publishedDate;
  final List<String> tags;
  final double rating;
  final int ratingCount;
  final bool isDownloaded;
  final String? localPath;

  const Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    this.coverUrl,
    required this.pdfUrl,
    required this.categoryId,
    required this.createdAt,
    required this.updatedAt,
    required this.totalPages,
    this.isbn,
    this.publisher,
    this.publishedDate,
    this.tags = const [],
    this.rating = 0.0,
    this.ratingCount = 0,
    this.isDownloaded = false,
    this.localPath,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      description: json['description'] as String,
      coverUrl: json['cover_url'] as String?,
      pdfUrl: json['pdf_url'] as String,
      categoryId: json['category_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      totalPages: json['total_pages'] as int,
      isbn: json['isbn'] as String?,
      publisher: json['publisher'] as String?,
      publishedDate: json['published_date'] != null
          ? DateTime.parse(json['published_date'] as String)
          : null,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      ratingCount: json['rating_count'] as int? ?? 0,
      isDownloaded: json['is_downloaded'] as bool? ?? false,
      localPath: json['local_path'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'cover_url': coverUrl,
      'pdf_url': pdfUrl,
      'category_id': categoryId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'total_pages': totalPages,
      'isbn': isbn,
      'publisher': publisher,
      'published_date': publishedDate?.toIso8601String(),
      'tags': tags,
      'rating': rating,
      'rating_count': ratingCount,
      'is_downloaded': isDownloaded,
      'local_path': localPath,
    };
  }

  Book copyWith({
    String? id,
    String? title,
    String? author,
    String? description,
    String? coverUrl,
    String? pdfUrl,
    String? categoryId,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? totalPages,
    String? isbn,
    String? publisher,
    DateTime? publishedDate,
    List<String>? tags,
    double? rating,
    int? ratingCount,
    bool? isDownloaded,
    String? localPath,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      description: description ?? this.description,
      coverUrl: coverUrl ?? this.coverUrl,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      categoryId: categoryId ?? this.categoryId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      totalPages: totalPages ?? this.totalPages,
      isbn: isbn ?? this.isbn,
      publisher: publisher ?? this.publisher,
      publishedDate: publishedDate ?? this.publishedDate,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localPath: localPath ?? this.localPath,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        author,
        description,
        coverUrl,
        pdfUrl,
        categoryId,
        createdAt,
        updatedAt,
        totalPages,
        isbn,
        publisher,
        publishedDate,
        tags,
        rating,
        ratingCount,
        isDownloaded,
        localPath,
      ];
}
