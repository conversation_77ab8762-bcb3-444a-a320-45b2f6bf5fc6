class AppConstants {
  // App Info
  static const String appName = 'BookApp';
  static const String appVersion = '1.0.0';
  
  // Supabase Configuration
  // TODO: Replace with your actual Supabase URL and anon key
  static const String supabaseUrl = 'https://your-project.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
  
  // Storage
  static const String booksTable = 'books';
  static const String categoriesTable = 'categories';
  static const String bookmarksTable = 'bookmarks';
  static const String notesTable = 'notes';
  static const String progressTable = 'reading_progress';
  static const String downloadsTable = 'downloads';
  
  // Storage Buckets
  static const String booksBucket = 'books';
  static const String coversBucket = 'covers';
  
  // Local Storage Keys
  static const String userPrefsKey = 'user_preferences';
  static const String themeKey = 'theme_mode';
  static const String fontSizeKey = 'font_size';
  static const String brightnessKey = 'brightness';
  static const String readingModeKey = 'reading_mode';
  
  // Reading Settings
  static const double defaultFontSize = 16.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  static const double defaultBrightness = 1.0;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Error Messages
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String authError = 'Authentication failed. Please try again.';
  static const String downloadError = 'Failed to download book. Please try again.';
  static const String uploadError = 'Failed to upload file. Please try again.';
  static const String genericError = 'Something went wrong. Please try again.';
}
